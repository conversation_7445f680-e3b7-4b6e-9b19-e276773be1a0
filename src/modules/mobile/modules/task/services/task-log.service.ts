import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { ApplyTaskLogDto, TaskLogFieldDto } from '../dto/apply-task-log.dto';
import { v4 as uuidv4 } from 'uuid';
import { StorageService } from '../../../../../common/modules/storage/services/storage.service';
import { LogTask } from '../../../../../common/modules/database/entities/log-task.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { LogTaskField } from '../../../../../common/modules/database/entities/log-task-field.entity';
import {
  Task,
  TaskType,
} from '../../../../../common/modules/database/entities/task.entity';
import { Log<PERSON>lert } from '../../../../../common/modules/database/entities/log-alert.entity';
import { Role } from '../../../../../common/modules/database/entities/role.entity';
import * as dayjs from 'dayjs';
import * as isBetween from 'dayjs/plugin/isBetween';
import * as Timezone from 'dayjs/plugin/timezone';
import { DeviceHeader } from '../../../../../common/decorators/current-device.decorator';
import { Device } from '../../../../../common/modules/database/entities/device.entity';

interface PreProcessingData {
  latitude: number;
  longitude: number;
  fields: {
    id: string;
    field_type_id: string;
    field_type_name: string;
    task_field_name: string;
    value: string | Express.Multer.File;
  }[];
}

@Injectable()
export class TaskLogService {
  constructor(
    private storageService: StorageService,
    private dataSource: DataSource,
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
  ) {}

  async apply(
    taskId: string,
    data: ApplyTaskLogDto,
    files: Express.Multer.File[],
    user: User,
    deviceFromHeader: DeviceHeader,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const uploadedFiles: { fileName: string }[] = [];

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        if (!getDevice) {
          throw new NotFoundException('Device not found');
        }
        device = getDevice;
      }

      // Validate Task ID Exists
      const task = await this.taskRepository.findOne({
        where: {
          id: parseInt(taskId),
          parent_branch_id: user.parent_branch_id,
        },
        relations: ['branch', 'parent_branch', 'fields', 'branch.timezone'],
      });
      if (!task) {
        throw new NotFoundException('Task not found');
      }

      //Validate allowed task submitted time
      this.validateAllowedTime(
        task,
        dayjs(data.original_submitted_time).toDate(),
        task.branch.timezone.timezone_name,
      );

      // Validate task fields
      this.validateTaskFields(task, data.fields);

      // Validate files first
      this.validateFiles(data, files);

      // Get Role
      const getRole = await this.roleRepository.findOne({
        where: { id: user.role_id, parent_branch_id: user.parent_branch_id },
      });

      if (!getRole) {
        throw new NotFoundException('Role not found');
      }

      const preProcessingData: PreProcessingData = {
        latitude: data.latitude,
        longitude: data.longitude,
        fields: data.fields.map(field => {
          let value: string | Express.Multer.File = field.value;
          if (field.field_type_name === 'image') {
            const find = files.find(
              file => file.fieldname === `file_${field.id}`,
            );
            if (find) {
              value = find;
            }
          }
          return {
            ...field,
            value,
          };
        }),
      };

      const uploadedPhoto = new Map<
        string,
        {
          photoPublicUrl: string;
          fileName: string;
        }
      >();

      // Upload all photos
      for (const field of preProcessingData.fields) {
        if (field.field_type_name === 'image') {
          const find = files.find(
            file => file.fieldname === `file_${field.id}`,
          );
          if (find) {
            const fileExtension = find.originalname.split('.').pop();
            const fileUuid = uuidv4();
            const fileName = `log-task/${fileUuid}.${fileExtension}`;
            const photoPublicUrl = await this.storageService.uploadFile(
              find,
              fileName,
            );
            uploadedPhoto.set(`file_${field.id}`, {
              photoPublicUrl,
              fileName,
            });
            uploadedFiles.push({ fileName });
          }
        }
      }

      const logTask = new LogTask();
      logTask.uuid = uuidv4();
      logTask.parent_branch_id = task.parent_branch_id;
      logTask.branch_id = task.branch_id;
      logTask.branch_name = task.branch.branch_name;
      logTask.task_id = parseInt(taskId);
      logTask.task_name = task.task_name;
      logTask.task_type = task.task_type;
      logTask.task_start_time = task.start_time;
      logTask.task_end_time = task.end_time;
      logTask.task_allowed_time = task.allowed_time;
      logTask.role_id = user.role_id;
      logTask.role_name = getRole.role_name;
      logTask.user_id = user.id;
      logTask.user_name = user.name;
      logTask.timezone_id = task.branch.timezone.id;
      logTask.timezone_name = task.branch.timezone.timezone_name;
      logTask.latitude = preProcessingData.latitude;
      logTask.longitude = preProcessingData.longitude;
      if (device) {
        logTask.device_id = device.id;
        logTask.device_name = device.device_name;
      }
      logTask.original_submitted_time = dayjs(
        data.original_submitted_time,
      ).toDate();
      logTask.event_time = new Date();

      const savedLogTask = await queryRunner.manager.save(LogTask, logTask);

      const logTaskFields: LogTaskField[] = [];
      for (const field of preProcessingData.fields) {
        const logTaskField = new LogTaskField();
        logTaskField.log_task_id = savedLogTask.id;
        logTaskField.field_type_id = parseInt(field.field_type_id);
        logTaskField.field_type_name = field.field_type_name;
        logTaskField.task_field_name = field.task_field_name;
        if (field.field_type_name === 'image') {
          const find = uploadedPhoto.get(`file_${field.id}`);
          if (find) {
            logTaskField.field_type_value = find.photoPublicUrl;
          }
        } else {
          logTaskField.field_type_value = field.value as string;
        }
        logTaskFields.push(logTaskField);
      }
      await queryRunner.manager.save(LogTaskField, logTaskFields);

      const logAlert = new LogAlert();
      logAlert.uuid = uuidv4();
      logAlert.alert_event_id = 2;
      logAlert.alert_event_name = 'Task Submitted';
      logAlert.log_id = savedLogTask.id;
      logAlert.log_uuid = savedLogTask.uuid;
      logAlert.reference_name = task.task_name;
      logAlert.parent_branch_id = task.parent_branch_id;
      logAlert.branch_id = task.branch_id;
      logAlert.user_id = user.id;
      logAlert.role_id = user.role_id;
      logAlert.payload_data = {
        type: 'task',
        task: task,
        logTask: savedLogTask,
        fields: logTaskFields,
      };
      logAlert.deleted_on_dashboard = false;
      logAlert.original_submitted_time = savedLogTask.original_submitted_time;
      logAlert.event_time = savedLogTask.event_time;

      await queryRunner.manager.save(LogAlert, logAlert);
      await queryRunner.commitTransaction();

      return {
        message: 'Log task created successfully',
        data: savedLogTask,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      // Delete all uploaded files if there's an error
      for (const file of uploadedFiles) {
        try {
          await this.storageService.deleteFiles(file.fileName);
        } catch (deleteError) {
          // Log the error but don't throw it
          console.error(`Failed to delete file ${file.fileName}:`, deleteError);
        }
      }

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private validateFiles(data: ApplyTaskLogDto, files: Express.Multer.File[]) {
    // Create a map of expected photo fields
    const expectedPhotos = new Map<string, boolean>();
    const receivedPhotos = new Map<string, boolean>();

    // Collect expected photo fields
    data.fields.forEach(field => {
      if (
        field.field_type_name === 'image' ||
        field.field_type_name === 'signature'
      ) {
        expectedPhotos.set(field.value, true);
      }
    });

    // Check received files
    files.forEach(file => {
      // Validate file name format
      if (!file.fieldname.match(/^file_\d+$/)) {
        throw new BadRequestException(
          `Invalid file field name: ${file.fieldname}. Expected format: file_[ID]`,
        );
      }

      // Mark as received
      receivedPhotos.set(file.fieldname, true);

      // Check if this photo was expected
      if (!expectedPhotos.has(file.fieldname)) {
        throw new BadRequestException(
          `Unexpected file received: ${file.fieldname}`,
        );
      }

      // Validate file type
      if (!file.mimetype.startsWith('image/')) {
        throw new BadRequestException(
          `Invalid file type for ${file.fieldname}. Only images are allowed`,
        );
      }
    });

    // Check if all expected photos were received
    expectedPhotos.forEach((_, photoField) => {
      if (!receivedPhotos.has(photoField)) {
        throw new BadRequestException(
          `Missing required photo file: ${photoField}`,
        );
      }
    });
  }

  private validateTaskFields(task: Task, submittedFields: TaskLogFieldDto[]) {
    // Buat map dari field yang terdaftar pada task
    const taskFields = new Map(
      task.fields.map(field => [field.id.toString(), field]),
    );

    // Validasi setiap field yang disubmit
    for (const field of submittedFields) {
      const taskField = taskFields.get(field.id);

      if (!taskField) {
        throw new BadRequestException(
          `Field with ID ${field.id} is not registered in this task`,
        );
      }

      // Validasi tipe field
      if (taskField.field_type_id.toString() !== field.field_type_id) {
        throw new BadRequestException(
          `Invalid field type for field ${field.id}. Expected: ${taskField.field_type_id}, got: ${field.field_type_id}`,
        );
      }

      // Validasi nama field
      if (taskField.task_field_name !== field.task_field_name) {
        throw new BadRequestException(
          `Invalid field name for field ${field.id}. Expected: ${taskField.task_field_name}, got: ${field.task_field_name}`,
        );
      }
    }
  }

  private validateAllowedTime(
    task: Task,
    submittedTime: Date,
    timezone?: string,
  ) {
    //Initiaize plugin dayjs isBetween
    dayjs.extend(isBetween);
    dayjs.extend(Timezone);
    //Declare start and end time
    const start = dayjs(task.start_time).tz(timezone).toDate();
    const end = dayjs(task.end_time).tz(timezone).toDate();
    //Check if task is repeating
    if (task.task_type === TaskType.REPEATING) {
      start.setFullYear(
        dayjs().get('year'),
        dayjs().get('month'),
        dayjs().get('date'),
      );
      end.setFullYear(
        dayjs().get('year'),
        dayjs().get('month'),
        dayjs().get('date'),
      );
    }
    //Check if submitted time is between start and end time
    if (!dayjs(submittedTime).isBetween(start, end)) {
      if (task.task_type === TaskType.REPEATING) {
        throw new BadRequestException(
          `Submitted time ${dayjs(submittedTime).tz(timezone).format('HH:mm')} is not within the allowed time range between ${dayjs(start).tz(timezone).format('HH:mm')} and ${dayjs(end).tz(timezone).format('HH:mm')} at Timezone ${timezone}`,
        );
      } else {
        throw new BadRequestException(
          `Submitted time ${dayjs(submittedTime).tz(timezone).format('YYYY-MM-DD HH:mm')} is not within the allowed time range between ${dayjs(start).tz(timezone).format('YYYY-MM-DD HH:mm')} and ${dayjs(end).tz(timezone).format('YYYY-MM-DD HH:mm')} at Timezone ${timezone}`,
        );
      }
    }
  }
}
