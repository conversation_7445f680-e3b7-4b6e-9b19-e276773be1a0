import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository, SelectQueryBuilder } from 'typeorm';
import * as dayjs from 'dayjs';
import { Alert } from '../../database/entities/alert.entity';
import { UserBranch } from '../../database/entities/user-branch.entity';
import { SendgridService } from '../../mailer/sendgrid/services/sendgrid.service';
import { SendgridSendMessageInterface } from '../../mailer/sendgrid/interfaces/sendgird.interface';
import { IAlertParameters } from '../interfaces/alert-common.interface';
import { ActivityLogGenerateDocumentService } from '../../analytics/activity/services/activity-log-generate-document.service';
import { Timezone } from '../../database/entities/timezone.entity';
import { TaskLogGenerateDocumentService } from '../../analytics/task/services/task-log-generate-document.service';
import { FormsLogGenerateDocumentService } from '../../analytics/form/services/forms-log-generate-document.service';
import { AlarmLogGenerateDocumentService } from '../../analytics/alarm/services/alarm-log-generate-document.service';
import { CheckpointActivityLogGenerateDocumentService } from '../../analytics/checkpoint-activity/services/checkpoint-activity-log-generate-document.service';
import { SignInOutLogGenerateDocumentService } from '../../analytics/sign-in-out/services/sign-in-out-log-generate-document.service';

@Injectable()
export class AlertCommonService {
  private alertQueryBuilder: SelectQueryBuilder<Alert>;

  constructor(
    @InjectRepository(Alert)
    private alertRepository: Repository<Alert>,
    @InjectRepository(UserBranch)
    private userBranchRepository: Repository<UserBranch>,
    private activityLogGenerateDocumentService: ActivityLogGenerateDocumentService,
    private taskLogGenerateDocumentService: TaskLogGenerateDocumentService,
    private formLogGenerateDocumentService: FormsLogGenerateDocumentService,
    private alarmLogGenerateDocumentService: AlarmLogGenerateDocumentService,
    private checkpointActivityLogGenerateDocumentService: CheckpointActivityLogGenerateDocumentService,
    private signInOutLogGenerateDocumentService: SignInOutLogGenerateDocumentService,
    private sendgridService: SendgridService,
  ) {
    this.alertQueryBuilder = this.alertRepository
      .createQueryBuilder('alert')
      .leftJoinAndSelect('alert.recipients', 'recipients')
      .innerJoin('alert_branches', 'ab', 'ab.alert_id = alert.id')
      .innerJoinAndSelect('alert.parent_branch', 'parent_branch')
      .innerJoinAndSelect('parent_branch.timezone', 'timezone')
      .leftJoin('alert.conditions', 'conditions')
      .andWhere('alert.active = :active', { active: true });
  }

  async processAlert(logData: IAlertParameters) {
    // Get matching 'or' alerts
    const matchingOrAlerts = await this.getAlerts(logData, 'OR');

    // Get matching 'and' alerts
    const matchingAndAlerts = await this.getAlerts(logData, 'AND');

    // Combine alerts
    const alerts = [...matchingOrAlerts, ...matchingAndAlerts];

    // Process alerts (send emails, etc.)
    for (const alert of alerts) {
      new Promise(async (resolve, reject) => {
        if (parseInt(alert.alert_action_id as any) === 1) {
          const createPdf = await this.generatePdf(
            logData,
            alert.parent_branch.timezone,
          );

          // Send email
          const payloadSendEmails: SendgridSendMessageInterface = {
            to: alert.recipients.map(recipient => recipient.recipient_contact),
            subject: alert.subject,
            html: alert.message,
            text: alert.message,
          };

          if (createPdf) {
            payloadSendEmails.attachments = [
              {
                content: createPdf.buffer.toString('base64'),
                filename: createPdf.filename,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ];
            await this.sendgridService.sendEmail(payloadSendEmails);
          }
        }
      })
        .then()
        .catch(err => {
          console.log('Error sending email: ', err);
        });
    }

    return matchingOrAlerts;
  }

  private async getAlerts(logData: IAlertParameters, condition: 'OR' | 'AND') {
    // Extract common properties
    const {
      userId,
      roleId,
      checkpointId,
      geofenceId,
      zoneId,
      deviceId,
      submittedDateTime,
      parentBranchId,
      alertEventId,
    } = logData;

    // Extract time-based properties
    const dayOfMonth = dayjs(submittedDateTime).date();
    const dayOfWeek = dayjs(submittedDateTime).day();
    const hours = dayjs(submittedDateTime).hour();
    const minutes = dayjs(submittedDateTime).minute();

    // Get user branches
    const userBranches = await this.userBranchRepository.find({
      where: {
        user_id: userId,
        active: true,
      },
    });
    const branchIds = userBranches.map(userBranch => userBranch.branch_id);

    const alertQueryBuilder = this.alertQueryBuilder
      .clone()
      .andWhere('alert.alert_event_id = :alertEventId', {
        alertEventId: alertEventId,
      })
      .andWhere('parent_branch_id = :parentBranchId', {
        parentBranchId: parentBranchId,
      })
      .andWhere('ab.branch_id IN (:...branchIds)', { branchIds });

    if (condition === 'OR') {
      // Apply OR conditions
      const orConditions = alertQueryBuilder
        .andWhere(
          'alert.alert_logical_condition_type = :alertLogicalConditionType',
          {
            alertLogicalConditionType: 'OR',
          },
        )
        .andWhere(
          new Brackets(qb => {
            qb.where(
              'NOT EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id)',
            ).orWhere(
              new Brackets(qb => {
                // User condition
                qb.where(this.userBracketCondition(userId));

                // Role condition
                qb.orWhere(this.roleBracketCondition(roleId));

                // Day of month condition
                qb.orWhere(this.dayOfMonthBracketCondition(dayOfMonth));

                // Day of week condition
                qb.orWhere(this.dayOfWeekBracketCondition(dayOfWeek));

                // Hours condition
                qb.orWhere(this.hoursBracketCondition(hours));

                // Minutes condition
                qb.orWhere(this.minutesBracketCondition(minutes));

                // Checkpoint condition
                // qb.orWhere(this.checkpointBracketCondition(checkpointId));

                // Geofence condition
                // qb.orWhere(this.geofenceBracketCondition(geofenceId));

                // Zone condition
                // qb.orWhere(this.zoneBracketCondition(zoneId));

                // Device condition
                qb.orWhere(this.deviceBracketCondition(deviceId));
              }),
            );
          }),
        );
      return orConditions.getMany();
    } else {
      // Apply AND conditions
      const andConditions = alertQueryBuilder
        .andWhere(
          'alert.alert_logical_condition_type = :alertLogicalConditionType',
          {
            alertLogicalConditionType: 'AND',
          },
        )
        .andWhere(
          new Brackets(qb => {
            qb.where(
              'EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id)',
            ).orWhere(
              new Brackets(qb => {
                // User condition
                qb.where(this.userBracketCondition(userId));

                // Role condition
                qb.andWhere(this.roleBracketCondition(roleId));

                // Day of month condition
                qb.andWhere(this.dayOfMonthBracketCondition(dayOfMonth));

                // Day of week condition
                qb.andWhere(this.dayOfWeekBracketCondition(dayOfWeek));

                // Hours condition
                qb.andWhere(this.hoursBracketCondition(hours));

                // Minutes condition
                qb.andWhere(this.minutesBracketCondition(minutes));

                // Checkpoint condition
                // qb.andWhere(this.checkpointBracketCondition(checkpointId));

                // Geofence condition
                // qb.andWhere(this.geofenceBracketCondition(geofenceId));

                // Zone condition
                // qb.andWhere(this.zoneBracketCondition(zoneId));

                // Device condition
                qb.andWhere(this.deviceBracketCondition(deviceId));
              }),
            );
          }),
        );
      return andConditions.getMany();
    }
  }

  private userBracketCondition(userId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :userConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :userEqual)",
        {
          userConditionTypeIdEqual: 1,
          userEqual: userId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :userConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :userNotEqual)",
        {
          userConditionTypeIdNotEqual: 1,
          userNotEqual: userId,
        },
      );
    });
  }

  private roleBracketCondition(roleId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :roleConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :roleEqual)",
        {
          roleConditionTypeIdEqual: 2,
          roleEqual: roleId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :roleConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :roleNotEqual)",
        {
          roleConditionTypeIdNotEqual: 2,
          roleNotEqual: roleId,
        },
      );
    });
  }

  private dayOfMonthBracketCondition(dayOfMonth: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :dayOfMonthConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :dayOfMonthEqual)",
        {
          dayOfMonthConditionTypeIdEqual: 3,
          dayOfMonthEqual: dayOfMonth,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :dayOfMonthConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :dayOfMonthNotEqual)",
        {
          dayOfMonthConditionTypeIdNotEqual: 3,
          dayOfMonthNotEqual: dayOfMonth,
        },
      );
    });
  }

  private dayOfWeekBracketCondition(dayOfWeek: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :dayOfWeekConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :dayOfWeekEqual)",
        {
          dayOfWeekConditionTypeIdEqual: 4,
          dayOfWeekEqual: dayOfWeek,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :dayOfWeekConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :dayOfWeekNotEqual)",
        {
          dayOfWeekConditionTypeIdNotEqual: 4,
          dayOfWeekNotEqual: dayOfWeek,
        },
      );
    });
  }

  private hoursBracketCondition(hours: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :hoursConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :hoursEqual)",
        {
          hoursConditionTypeIdEqual: 5,
          hoursEqual: hours,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :hoursConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :hoursNotEqual)",
        {
          hoursConditionTypeIdNotEqual: 5,
          hoursNotEqual: hours,
        },
      );
    });
  }

  private minutesBracketCondition(minutes: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :minutesConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :minutesEqual)",
        {
          minutesConditionTypeIdEqual: 6,
          minutesEqual: minutes,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :minutesConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :minutesNotEqual)",
        {
          minutesConditionTypeIdNotEqual: 6,
          minutesNotEqual: minutes,
        },
      );
    });
  }

  private checkpointBracketCondition(checkpointId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :checkpointConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :checkpointEqual)",
        {
          checkpointConditionTypeIdEqual: 7,
          checkpointEqual: checkpointId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :checkpointConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :checkpointNotEqual)",
        {
          checkpointConditionTypeIdNotEqual: 7,
          checkpointNotEqual: checkpointId,
        },
      );
    });
  }

  private geofenceBracketCondition(geofenceId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :geofenceConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :geofenceEqual)",
        {
          geofenceConditionTypeIdEqual: 8,
          geofenceEqual: geofenceId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :geofenceConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :geofenceNotEqual)",
        {
          geofenceConditionTypeIdNotEqual: 8,
          geofenceNotEqual: geofenceId,
        },
      );
    });
  }

  private zoneBracketCondition(zoneId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :zoneConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :zoneEqual)",
        {
          zoneConditionTypeIdEqual: 9,
          zoneEqual: zoneId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :zoneConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :zoneNotEqual)",
        {
          zoneConditionTypeIdNotEqual: 9,
          zoneNotEqual: zoneId,
        },
      );
    });
  }

  private deviceBracketCondition(deviceId: number) {
    return new Brackets(qb => {
      qb.where(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :deviceConditionTypeIdEqual AND ac.alert_operator_condition_type = '=' AND ac.alert_condition_value_id = :deviceEqual)",
        {
          deviceConditionTypeIdEqual: 10,
          deviceEqual: deviceId,
        },
      ).orWhere(
        "EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id AND ac.alert_condition_type_id = :deviceConditionTypeIdNotEqual AND ac.alert_operator_condition_type = '!=' AND ac.alert_condition_value_id != :deviceNotEqual)",
        {
          deviceConditionTypeIdNotEqual: 10,
          deviceNotEqual: deviceId,
        },
      );
    });
  }

  // Generate PDF
  private generatePdf(
    params: Pick<
      IAlertParameters,
      | 'alertEventId'
      | 'logActivity'
      | 'logTask'
      | 'logForm'
      | 'logAlarm'
      | 'logCheckpoint'
      | 'logSignInOut'
    >,
    timezone: Timezone,
  ) {
    // Activity
    if (params.alertEventId === 1 && params.logActivity) {
      return this.activityLogGenerateDocumentService.generatePDFById(
        params.logActivity,
        timezone,
      );
    } else if (params.alertEventId === 2 && params.logTask) {
      // Task
      return this.taskLogGenerateDocumentService.generatePDFById(
        params.logTask,
        timezone,
      );
    } else if (params.alertEventId === 3 && params.logForm) {
      // Form
      return this.formLogGenerateDocumentService.generatePDFById(
        params.logForm,
        timezone,
      );
    } else if (params.alertEventId === 4 && params.logAlarm) {
      // Alarm
      return this.alarmLogGenerateDocumentService.generatePDFById(
        params.logAlarm,
        timezone,
      );
    } else if (params.alertEventId === 5 && params.logCheckpoint) {
      // Checkpoint
      return this.checkpointActivityLogGenerateDocumentService.generatePDFById(
        params.logCheckpoint,
        timezone,
      );
    } else if ([6, 7].includes(params.alertEventId) && params.logSignInOut) {
      // Sign In/Out
      return this.signInOutLogGenerateDocumentService.generatePDFById(
        params.logSignInOut,
        timezone,
      );
    }
  }
}
