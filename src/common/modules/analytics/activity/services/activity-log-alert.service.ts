import { Injectable } from '@nestjs/common';
import { Activity } from '../../../database/entities/activity.entity';
import { LogActivity } from '../../../database/entities/log-activity.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class ActivityLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    activity: Activity,
    logActivity: LogActivity,
    user: User,
  ) {
    // Check if logActivity already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_id: logActivity.id },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 1;
    logAlert.alert_event_name = 'Activity Submitted';
    logAlert.log_id = logActivity.id;
    logAlert.log_uuid = logActivity.uuid;
    logAlert.reference_name = activity.activity_name;
    logAlert.parent_branch_id = user.parent_branch_id;
    logAlert.branch_id = activity.id;
    logAlert.user_id = user.id;
    logAlert.role_id = user.role_id;
    logAlert.payload_data = {
      type: 'activity',
      activity: activity,
      logActivity: logActivity,
    };
    logAlert.event_time = logActivity.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logActivity.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
